package com.mossbets.integrations.utils;

import com.google.gson.JsonArray;
import com.mossbets.integrations.config.TransactionConstants;
import com.mossbets.integrations.controllers.resources.Configurations;
import com.mossbets.integrations.controllers.resources.DB;
import com.mossbets.integrations.utils.crypto.HmacService;
import com.mossbets.integrations.utils.props.LeaderboardService;
import com.mossbets.integrations.utils.props.Props;

import io.agroal.api.AgroalDataSource;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@SuppressWarnings("all")
public class WalletUtils extends Configurations {

    @Inject
    public DB dbSource;

    @Inject
    private Props props;

    @Inject
    HmacService hmacService;

    @Inject
    LeaderboardService leaderboardService;

    @Inject
    Queue Queue;
    /**
     * Gets the player's balance and bonus information from the database
     *
     * @param profileId The ID of the player's profile
     * @return Map containing balance, bonus, and status, or null if not found
     */
    @WithSpan
    public Map<String, Object> getPlayerBalance(String profileId) {
        String sql = "SELECT p.name as name, IFNULL(pb.balance, 0) as balance, IFNULL(bonus, 0) as bonus "
                + " FROM profile_balance pb JOIN profile p ON pb.profile_id = p.id WHERE profile_id = ? LIMIT 1";

        try (Connection conn = dbSource.getReadDB("main").getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, profileId);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("name", rs.getBigDecimal("name"));
                    result.put("balance", rs.getBigDecimal("balance"));
                    result.put("bonus", rs.getBigDecimal("bonus"));
                    return result;
                }
            }

        } catch (SQLException e) {
            logger.error("Error fetching player balance for profile: " + profileId, e);
        }

        Map<String, Object> defaultValues = new HashMap<>();
        defaultValues.put("balance", BigDecimal.ZERO);
        // defaultValues.put("bonus", BigDecimal.ZERO);
        return defaultValues;
    }

    @WithSpan
    public Map<String, Object> getPlayerDetails(String profileId) {
        String sql = "SELECT msisdn, ip_address FROM profile WHERE CAST(id AS CHAR) = ?";
        try (Connection conn = dbSource.getReadDB("main").getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, profileId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("msisdn", rs.getString("msisdn"));
                    result.put("ip_address", rs.getString("ip_address"));
                    return result;
                }
            }
        } catch (SQLException e) {
            logger.error("Error fetching player balance for profile: " + profileId, e);
        }

        return null;
    }

    @WithSpan
    public boolean getInfo(String profileId) {
        String sql = "SELECT id  FROM profile_login WHERE CAST(profile_id AS CHAR) = ? AND status = 6 LIMIT 1";
        try (Connection conn = dbSource.getReadDB("main").getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, profileId);
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }

        } catch (SQLException e) {
            logger.error("Error fetching player balance for profile: " + profileId, e);
        }

        return false;
    }

    /**
     * Updates player balance based on bet outcome
     *
     * @param userId Player's ID
     * @param gameId Game ID
     * @param reference Bet reference
     * @param amount Amount to update (positive for win, negative for loss)
     * @param odds Bet odds (default 0.0)
     * @param betExtraData Additional bet data as JSON string
     * @param timestamp Transaction timestamp
     * @param type Transaction type (REFUND, ADJUSTMENT, BONUS, or null for regular win/loss)
     * @return Transaction ID if successful, null otherwise 
     */

    @WithSpan
    public String updatePlayerBalance(String userId,  String reference, double amount,
                                      JsonObject betExtraData, String timestamp, String type) {
        Connection connBet = null;
        Connection connTrxn = null;
        Connection connProfile = null;
        boolean isRisk = false;
        String messageOut=null;
        double odds = 0.0;
        double betAmount = 0.0;

        try {
            connBet = dbSource.getWriteDB("virtual").getConnection();
            connTrxn = dbSource.getWriteDB("trxn").getConnection();
            connProfile = dbSource.getWriteDB("main").getConnection();

            connBet.setAutoCommit(false);
            connTrxn.setAutoCommit(false);
            connProfile.setAutoCommit(false);

            String betCheckSql = "SELECT bet_id, profile_id, bet_transaction_id, bet_credit_transaction_id, " +
            "extra_data, status, (bet_amount - excise_tax) AS stake_after_tax, created_by " +
            "FROM mossbets_bets.virtuals_bet " +
            "WHERE profile_id = ? AND game_id = ? AND status = 0 LIMIT 1";
        

            Map<String, Object> betCheck = null;
            try (PreparedStatement stmt = connBet.prepareStatement(betCheckSql)) {
                stmt.setString(1, userId);
                stmt.setString(2, reference);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        betCheck = new HashMap<>();
                        betCheck.put("bet_id", rs.getLong("bet_id"));
                        betCheck.put("profile_id", rs.getLong("profile_id"));
                        betCheck.put("bet_transaction_id", rs.getLong("bet_transaction_id"));
                        betCheck.put("bet_credit_transaction_id", rs.getObject("bet_credit_transaction_id", Long.class));
                        betCheck.put("extra_data", rs.getString("extra_data"));
                        betCheck.put("status", rs.getInt("status"));
                        betCheck.put("stake_after_tax", rs.getDouble("stake_after_tax"));
                        betCheck.put("created_by", rs.getString("created_by"));
                      
                    }
                }
            }

            if (betCheck == null) {
                return null;
            }
            double stake = Double.parseDouble(betCheck.get("stake_after_tax").toString());
            odds = amount/stake;

            betAmount = Double.parseDouble(betCheck.get("stake_after_tax").toString());

            JsonObject existingExtraData = new JsonObject();
            if (betCheck.get("extra_data") != null) {
                try {
                    existingExtraData = JsonParser.parseString((String) betCheck.get("extra_data")).getAsJsonObject();
                } catch (Exception e) {
                    logger.warn("Failed to parse existing extra data: " + e.getMessage());
                }
            }

            if (betExtraData != null) {
                // Merge betExtraData into existingExtraData
                for (String key : betExtraData.keySet()) {
                    existingExtraData.add(key, betExtraData.get(key));
                }
            }

            boolean isWin = amount > 0;
            int status = isWin ? 1 : 2;

            String updateBetSql = "UPDATE mossbets_bets.virtuals_bet SET status = ?, extra_data = ?, total_odd = ?";
            List<Object> updateParams = new ArrayList<>();
            updateParams.add(status);
            updateParams.add(existingExtraData.toString());
            updateParams.add(odds);

            String transactionId = null;

            if (isWin) {
                if(leaderboardService.updateOdds(userId, odds, betAmount)){
                    logger.info("Successfully updated odds on Leaderboard for user: " + userId);
                }
                double maxWin = amount;
                double riskApproval=0.0;
                String maxWinSql = "SELECT max_win,risk_approval_amount FROM bet_limits WHERE bet_name = ? AND client_id = ?";
                try (PreparedStatement stmt = connBet.prepareStatement(maxWinSql)) {
                    stmt.setString(1, "SHACKSGAMES");
                    stmt.setInt(2, 1);
                    try (ResultSet rs = stmt.executeQuery()) {
                        if (rs.next() && rs.getDouble("max_win") > 0) {
                            riskApproval=rs.getDouble("risk_approval_amount");
                            double maxAllowed = rs.getDouble("max_win");
                            if (amount > maxAllowed) {
                                maxWin = maxAllowed;
                            }
                        }
                    }
                }

                // Determine transaction type
                int transactionTypeId = isWin ? 1 : 2; 
                int referenceTypeId;
                referenceTypeId = 4; 

                String source = "SHACKSGAMES" + (isWin ? "_PAYOUT" : "_ADJUSTMENT");
                String description = "SHACKSGAMES (" + reference + ") Bet " + (isWin ? "Credit" : "Debit");
                // Create transaction
                String transactionSql = "INSERT INTO mossbets_transactions.transaction " +
                        "(profile_id, reference_type_id, transaction_type_id, reference_id, " +
                        "amount, currency, source, description, extra_data, created_at) " +
                        "VALUES (?, ?, ?, ?, ?, 'KES', ?, ?, ?, NOW())";

                JsonObject extraDataBuilder = new JsonObject();
                extraDataBuilder.addProperty("total_events", 1);
                extraDataBuilder.addProperty("bet_id", (long) betCheck.get("bet_id"));
                extraDataBuilder.addProperty("trxn_id", reference);
                extraDataBuilder.addProperty("trxn_date", timestamp);

                try (PreparedStatement stmt = connTrxn.prepareStatement(
                        transactionSql, PreparedStatement.RETURN_GENERATED_KEYS)) {

                    stmt.setLong(1, (long) betCheck.get("profile_id"));
                    stmt.setInt(2, props.evoPayout());
                    stmt.setInt(3, transactionTypeId);
                    stmt.setLong(4, (long) betCheck.get("bet_transaction_id"));
                    stmt.setDouble(5, maxWin);
                    stmt.setString(6, source);
                    stmt.setString(7, description);
                    stmt.setString(8, extraDataBuilder.toString());

                    stmt.executeUpdate();

                    try (ResultSet rs = stmt.getGeneratedKeys()) {
                        if (rs.next()) {
                            transactionId = String.valueOf(rs.getLong(1));
                        }
                    }
                }
                // Update bet with transaction ID
                 updateBetSql += ", possible_win= ?";
                 updateParams.add(maxWin);

                updateBetSql += ", bet_credit_transaction_id = ?";
                updateParams.add(transactionId);

                // Update player balance
                String profileCheckSql = "SELECT balance, bonus FROM profile_balance WHERE profile_id = ?";
                Map<String, Object> profileBalance = null;

                try (PreparedStatement stmt = connProfile.prepareStatement(profileCheckSql)) {
                    stmt.setString(1, userId);

                    try (ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            profileBalance = new HashMap<>();
                            profileBalance.put("balance", rs.getDouble("balance"));
                            profileBalance.put("bonus", rs.getDouble("bonus"));
                        }
                    }
                }
                String game = betCheck.get("created_by").toString();
                if(isWin && (maxWin>=riskApproval)){

                    messageOut ="Kindly Approve the Transaction "+ transactionId +" for the amount " + maxWin + "game "+"(" + game +")";

                    isRisk=true;

                    String transactionRiskSql = "INSERT INTO mossbets_transactions.transaction_pending_approval " +
                    "(profile_id, transaction_id, amount, currency,source, description, created_at) " +
                    "VALUES (?, ?, ?, 'KES','SOFTGAMING', ?, NOW())";

                    try (PreparedStatement stmt2 = connTrxn.prepareStatement(transactionRiskSql, PreparedStatement.RETURN_GENERATED_KEYS)) {
                        stmt2.setString(1,userId);
                        stmt2.setString(2,transactionId);
                        stmt2.setDouble(3, maxWin);
                        stmt2.setString(4,messageOut);
                        stmt2.executeUpdate();                
                    }
                    maxWin=0;
                }
                
                if (profileBalance == null) {
                    String insertBalanceSql = "INSERT INTO profile_balance " +
                            "(profile_id, currency, balance, bonus, created_at) " +
                            "VALUES (?, 'KES', ?, 0, NOW())";

                    try (PreparedStatement stmt = connProfile.prepareStatement(insertBalanceSql)) {
                        stmt.setString(1, userId);
                        stmt.setDouble(2, maxWin);
                        stmt.executeUpdate();
                    }
                } else {
                    if(maxWin>0){
                        String updateBalanceSql = "UPDATE profile_balance SET balance = balance + ?, updated_at = NOW() " +
                                    "WHERE profile_id = ?";
                        try (PreparedStatement stmt = connProfile.prepareStatement(updateBalanceSql)) {
                            stmt.setDouble(1, maxWin);
                            stmt.setString(2, userId);

                            int updated = stmt.executeUpdate();
                            if (updated < 1) {
                                throw new SQLException("Error updating player balance. KES " + maxWin + " failed!");
                            }
                        }
                }
                }

                // Finalize bet update
                updateBetSql += " WHERE bet_id = ?";
                updateParams.add(betCheck.get("bet_id"));

                try (PreparedStatement stmt = connBet.prepareStatement(updateBetSql)) {
                    for (int i = 0; i < updateParams.size(); i++) {
                        stmt.setObject(i + 1, updateParams.get(i));
                    }

                    int updated = stmt.executeUpdate();
                    if (updated < 1) {
                        throw new SQLException("Error updating bet final status (" + status + ") failed!");
                    }
                }
            } else {
                // Handle case with no payout (amount <= 0)
                existingExtraData.addProperty("note", "No payout due - amount is zero");

                updateBetSql += ", witholding_tax = 0, bet_credit_transaction_id = NULL, extra_data = ? " +
                        "WHERE bet_id = ?";
                try (PreparedStatement stmt = connBet.prepareStatement(updateBetSql)) {
                    stmt.setString(1, existingExtraData.toString());
                    stmt.setLong(2, (long) betCheck.get("bet_id"));

                    int updated = stmt.executeUpdate();
                    if (updated < 1) {
                        throw new SQLException("Updating bet with zero payout failed!");
                    }
                }
            }

            // Commit all transactions
            connBet.commit();
            connTrxn.commit();
            connProfile.commit();

            if(isRisk){
                try {
                    long outboxId = createSMSOutBox(userId,basics.SenderId, messageOut, "NOTIFICATIONS",dbSource.getWriteDB("main"));
                    JsonObject json = new JsonObject();
                    json.addProperty("campaign_id", outboxId);
                    json.addProperty("message_pages", 1);
                    json.addProperty("message", messageOut);
                    json.addProperty("sms-id", "QUICKSENDVERIFICATION");
                    json.addProperty("network_regex", 1);
                    json.addProperty("network", "SAFARICOM");
                    json.addProperty("alert_type", "TRANSACTIONAL");
                    json.addProperty("recipients", "0703968228");
                    json.addProperty("outbox_id", outboxId);
                    json.addProperty("short_code", basics.SenderId);
                    json.addProperty("gateway", 1);
                    json.addProperty("dlr_url", "");
                    json.addProperty("auth_token", "auth_token_api");
                    json.addProperty("date_created", Utilities.now("yyyy-MM-dd HH:mm:ss"));
                    
                    String jsonString = json.toString();
                    Queue.publishMessage(jsonString,
                            "OUTBOX",
                            "OUTBOX",
                            "OUTBOX");
                } catch (Exception e) {
                    logger.error(Utilities.getLogPreString("DepositApp")
                            + "|Send Outbox"
                            + "|Exception:" + e.getMessage());
                    throw e;
                }
            }
            return transactionId;

        } catch (SQLException e) {
            // Rollback all transactions in case of error
            try {
                if (connBet != null) connBet.rollback();
                if (connTrxn != null) connTrxn.rollback();
                if (connProfile != null) connProfile.rollback();
            } catch (SQLException ex) {
                logger.error("Error during rollback", ex);
            }
            logger.error("Error updating player balance", e);
            throw new RuntimeException("Failed to update player balance", e);
        } finally {

            // Close connections
            try {
                if (connBet != null) connBet.close();
                if (connTrxn != null) connTrxn.close();
                if (connProfile != null) connProfile.close();
            } catch (SQLException e) {
                logger.error("Error closing database connections", e);
            }
        }
    }

    @WithSpan
    public int isDuplicateAction(String type, String gameId, String reference, String userId, Instant startTime, BigDecimal amount) throws Exception {
     
        BigDecimal roundedAmount = amount.setScale(2, RoundingMode.HALF_UP);

        String sql = "SELECT bet_id,bet_amount,game_id, bet_reference" +
                     " FROM mossbets_bets.virtuals_bet " +
                     "WHERE profile_id = ? " +
                     " AND status = 0 "+
                     "AND bet_reference= ? " +
                    " LIMIT 1";
        
        try (Connection conn = dbSource.getReadDB("virtual").getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, userId);
            stmt.setString(2, reference);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    String game_Id = rs.getString("game_id");
                    String bet_reference = rs.getString("bet_reference");
                    BigDecimal bet_amount = rs.getBigDecimal("bet_amount").setScale(2, RoundingMode.HALF_UP);

                    Map<String, Object> balanceInfo = this.getPlayerBalance(userId);
                    BigDecimal currentBalance = new BigDecimal(balanceInfo.get("balance").toString());

                    if (type.equals("debit")) {
                        if (gameId.equals(game_Id) && bet_reference.equals(reference)) {
                            return 1;
                        } else {
                            return 0;
                        }
                    }
            }
            }
        } catch (SQLException e) {
            return 1;
        }
        return 0;
    }

    @WithSpan
    public int isDuplicateTransaction(String type,
                    String userId, String reference, Instant startTime,
                    BigDecimal amount){
                    // Round amount to 2 decimal places for consistent comparison
                    BigDecimal roundedAmount = amount.setScale(2, RoundingMode.HALF_UP);
                   // Query to get recent transactions for this user/game combination
                    String sql = "SELECT tid, bet_amount, status, action_id, credit_tid " +
                    "FROM mossbets_bets.virtuals_bet " +
                    "WHERE profile_id = ? AND game_id = ? " +
                    "ORDER BY created_at DESC ";

                    try (Connection conn = dbSource.getReadDB("virtual").getConnection();
                    PreparedStatement stmt = conn.prepareStatement(sql)) {

                    stmt.setString(1, userId);
                    stmt.setString(2, reference);

                    try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        String dbTid = rs.getString("tid");
                        BigDecimal dbAmount = rs.getBigDecimal("bet_amount").setScale(2, RoundingMode.HALF_UP);
                        String dbStatus = rs.getString("status");
                        String dbCreditTid = rs.getString("credit_tid");
                        String action_id = rs.getString("action_id");


                        if (reference.equals(dbTid) && reference.equals(dbCreditTid)) {
                            return 1;
                        }
                        // Scenario 1: Exact duplicate - same tid
                        if (reference.equals(dbTid)) {
                            return 2;
                        }

                        String sub_sql = "SELECT tid, possible_win, status, credit_tid " +
                                "FROM mossbets_bets.virtuals_bet " +
                                "WHERE credit_tid = ? ";

                        List<Map<String, Object>> results = new ArrayList<>();

                        try (Connection sub_conn = dbSource.getReadDB("virtual").getConnection();
                             PreparedStatement sub_stmt = conn.prepareStatement(sub_sql)){

                            sub_stmt.setString(1, reference);

                            BigDecimal bet_amount2 = BigDecimal.valueOf(0.00);
                            String dbCreditTid2 = "";
                            String dbStatus2 = "";

                            try (ResultSet result = sub_stmt.executeQuery()) {
                                while (result.next()) {
                                    Map<String, Object> row = new HashMap<>();
                                    row.put("tid", result.getString("tid"));
                                    row.put("possible_win", result.getBigDecimal("possible_win").setScale(2, RoundingMode.HALF_UP));
                                    row.put("status", result.getString("status"));
                                    row.put("credit_tid", result.getString("credit_tid"));
                                    results.add(row);
                                    bet_amount2 =  result.getBigDecimal("possible_win").setScale(2, RoundingMode.HALF_UP);
                                    dbCreditTid2 = result.getString("credit_tid");
                                }
                            }

                            if(!results.isEmpty()){
                                if(roundedAmount.compareTo(bet_amount2) != 0){
                                    return 2;
                                } else if(roundedAmount.compareTo(bet_amount2) == 0){
                                    return 1;
                                }
                            }

                        } catch (SQLException e) {
                            logger.error("Database error checking duplicates: {}", e.getMessage(), e);
                            throw e; // Rethrow or handle as needed
                        }
                        return 0;
                    }
                    }
                    } catch (SQLException e) {
                    logger.error("Database error checking duplicates: {}"+ e.getMessage());
                    return 1;
                    }

                    return 0; // No duplicates found
        }

    @WithSpan
    public String processDebit(String profileId, String gameId,String tid, String roundId,
                               BigDecimal amount, String reference, String description, String gameType,
                               JsonObject extraData) {
        Connection connBet = null;
        Connection connProfile = null;
        Connection connTrxn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;

        try {
            // Get database connections
            connBet = dbSource.getWriteDB("virtual").getConnection();
            connProfile = dbSource.getWriteDB("main").getConnection();
            connTrxn = dbSource.getWriteDB("trxn").getConnection();

            // Start transactions
            connBet.setAutoCommit(false);
            connProfile.setAutoCommit(false);
            connTrxn.setAutoCommit(false);

            String updateBalanceSql = "UPDATE profile_balance SET balance = balance - ?, " +
                    "bonus = bonus - 0 " +
                    "WHERE profile_id = ? " +
                    "AND balance  >= ? " +
                    "LIMIT 1";

            try (PreparedStatement balanceStmt = connProfile.prepareStatement(updateBalanceSql)) {
                balanceStmt.setBigDecimal(1, amount);
                balanceStmt.setString(2, profileId);
                balanceStmt.setBigDecimal(3, amount);
                int rowsUpdated = balanceStmt.executeUpdate();
                if (rowsUpdated == 0) {
                    // Insufficient balance
                    return null;
                }
            }
            // Create transaction record
            String transactionSql = "INSERT INTO mossbets_transactions.transaction (" +
                    "profile_id, reference_type_id, transaction_type_id, reference_id, " +
                    "amount, currency, source, description, extra_data, created_at) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            long transactionId;
            try (PreparedStatement trxnStmt = connTrxn.prepareStatement(
                    transactionSql, Statement.RETURN_GENERATED_KEYS)) {

                trxnStmt.setString(1, profileId);
                trxnStmt.setInt(2, props.evoStake());
                trxnStmt.setInt(3, TransactionConstants.TYPE_DEBIT);
                trxnStmt.setString(4, reference);
                trxnStmt.setBigDecimal(5, amount.negate());
                trxnStmt.setString(6, TransactionConstants.CURRENCY_KES);
                trxnStmt.setString(7, TransactionConstants.SOURCE_SHACKSGAMES_CASH_BET);
                trxnStmt.setString(8, gameType + " Bet Debit");

                Map<String, Object> playerDetails = getPlayerDetails(profileId);
                String msisdn = playerDetails.get("msisdn").toString();
                String ip_address = playerDetails.get("ip_address").toString();

                JsonObject extraDataBuilder = new JsonObject();
                extraDataBuilder.addProperty("msisdn", msisdn);
                extraDataBuilder.addProperty("ip_address", ip_address);
                extraDataBuilder.addProperty("total_events", 1);
                extraDataBuilder.addProperty("odds", 1.0f);
                extraDataBuilder.addProperty("payout", 0.0f);
                extraDataBuilder.addProperty("game_number", roundId);
                // Merge extraData into extraDataBuilder
                if (extraData != null) {
                    for (String key : extraData.keySet()) {
                        extraDataBuilder.add(key, extraData.get(key));
                    }
                }

                trxnStmt.setString(9, extraDataBuilder.toString());
                trxnStmt.executeUpdate();

                try (ResultSet generatedKeys = trxnStmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        transactionId = generatedKeys.getLong(1);
                    } else {
                        throw new SQLException("Failed to get transaction ID");
                    }
                }
                logger.info("Transaction ID: " + transactionId);
            }
            catch(SQLException e) {
              String findExistingSql = "SELECT bet_transaction_id FROM mossbets_bets.virtuals_bet " +
                        "WHERE profile_id = ? AND bet_reference = ? LIMIT 1";
                
                try (PreparedStatement findStmt = connBet.prepareStatement(findExistingSql)) {
                    findStmt.setString(1, profileId);
                    findStmt.setString(2, reference);
                    
                    try (ResultSet findRs = findStmt.executeQuery()) {
                        if (findRs.next()) {
                            String existingId = findRs.getString("bet_transaction_id");
                            connProfile.rollback();
                            connTrxn.rollback();
                            connBet.rollback();
                            
                            logger.info("Duplicate bet entry detected, using existing transaction: " + existingId);
                            return existingId;
                        }
                    }
                }
                throw e;
            }
            Map<String, Object> playerDetails = getPlayerDetails(profileId);
            String msisdn = playerDetails.get("msisdn").toString();
            String ip_address = playerDetails.get("ip_address").toString();

            JsonObject newExtraData = new JsonObject();
            newExtraData.addProperty("msisdn", msisdn);
            newExtraData.addProperty("ip_address", ip_address);
            if (extraData != null) {
                for (String key : extraData.keySet()) {
                    newExtraData.add(key, extraData.get(key));
                }
            }

            String betSql = "INSERT INTO mossbets_bets.virtuals_bet (" +
                            "client_id, profile_id, bet_currency, bet_amount, bet_reference, " +
                            "provider_name, bet_transaction_id, bet_type, total_games, " +
                            "total_odd, possible_win, witholding_tax, excise_tax, extra_data, " +
                            "created_by, created_at) " +
                            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            try (PreparedStatement betStmt = connBet.prepareStatement(betSql, Statement.RETURN_GENERATED_KEYS)) {

                int i = 1;
                betStmt.setInt(i++, 1);                       // client_id
                betStmt.setString(i++, profileId);
                betStmt.setString(i++, "KES");
                betStmt.setBigDecimal(i++, amount);
                betStmt.setString(i++, reference);
                betStmt.setString(i++, "SHACK-EVOLUTION");
                betStmt.setLong(i++, transactionId);
                betStmt.setInt(i++, 0); // bet_type
                betStmt.setInt(i++, 0); // total_games
                betStmt.setBigDecimal(i++, BigDecimal.ONE);     // total_odd
                betStmt.setBigDecimal(i++, BigDecimal.ZERO);     // possible_win
                betStmt.setBigDecimal(i++, BigDecimal.ZERO);    // witholding_tax
                betStmt.setBigDecimal(i++, BigDecimal.ZERO);    // excise_tax
                betStmt.setString(i++, newExtraData.toString());   // extra_data
                betStmt.setString(i++, gameType);  // created_by

                betStmt.executeUpdate();
                // Commit all transactions
                connProfile.commit();
                connTrxn.commit();
                connBet.commit();
                logger.info("Bet ID: " + transactionId);
                return String.valueOf(transactionId);
            }

        } catch (SQLException e) {
            // Rollback all transactions in case of error
            try {
                if (connProfile != null) connProfile.rollback();
                if (connTrxn != null) connTrxn.rollback();
                if (connBet != null) connBet.rollback();
            } catch (SQLException ex) {
                logger.error("Error during rollback", ex);
            }
            logger.error("Error processing debit", e);
            return null;
        } finally {
            try {
                if (rs != null) rs.close();
                if (stmt != null) stmt.close();
                if (connBet != null) connBet.close();
                if (connTrxn != null) connTrxn.close();
                if (connProfile != null) connProfile.close();
            } catch (SQLException e) {
                logger.error("Error closing database connections", e);
            }
        }
    }
    /**
     * Finds a transaction by TID for rollback purposes
     *
     * @param tid Transaction ID to find
     * @param userId User ID for additional validation
     * @return Map containing transaction details or null if not found
     */

    @WithSpan
    public Map<String, Object> findTransactionByTid(String tid, String userId) {
        String sql = "SELECT t.id, t.profile_id, t.amount, t.currency, t.reference_id, " +
                "t.transaction_type_id, t.source, t.description, t.extra_data, " +
                "vb.bet_id, vb.game_id, vb.bet_reference, vb.status " +
                "FROM mossbets_transactions.transaction t " +
                "LEFT JOIN mossbets_bets.virtuals_bet vb ON t.reference_id = vb.bet_reference " +
                "WHERE t.reference_id REGEXP ? AND t.profile_id = ? " +
                "ORDER BY t.created_at DESC LIMIT 1";

        try (Connection conn = dbSource.getReadDB("trxn").getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, tid);
            stmt.setString(2, userId);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("transaction_id", rs.getLong("id"));
                    result.put("profile_id", rs.getString("profile_id"));
                    result.put("amount", rs.getBigDecimal("amount"));
                    result.put("currency", rs.getString("currency"));
                    result.put("reference_id", rs.getString("reference_id"));
                    result.put("transaction_type_id", rs.getInt("transaction_type_id"));
                    result.put("source", rs.getString("source"));
                    result.put("description", rs.getString("description"));
                    result.put("extra_data", rs.getString("extra_data"));
                    result.put("bet_id", rs.getObject("bet_id"));
                    result.put("game_id", rs.getString("game_id"));
                    result.put("bet_reference", rs.getString("bet_reference"));
                    result.put("bet_status", rs.getObject("status"));
                    return result;
                }
            }

        } catch (SQLException e) {
            logger.error("Error finding transaction by TID: " + tid, e);
        }
        return null;
    }

    /**
     * Processes rollback for a specific transaction ID
     *
     * @param rollbackTid The TID of the transaction to rollback
     * @param newTid The new transaction ID for the rollback
     * @param userId User ID
     * @param gameId Game ID
     * @param actionId Action ID
     * @param amount Amount to rollback
     * @param extraData Additional data for the rollback transaction
     * @return Transaction ID if successful, null otherwise
     */

    @WithSpan
    public String processRollback(String rollbackTid, String newTid, String userId, String roundId, BigDecimal amount,
                                  JsonObject extraData) {

        Connection connBet = null;
        Connection connProfile = null;
        Connection connTrxn = null;
        try {
            // Find the original transaction
            Map<String, Object> originalTransaction = findTransactionByTid(rollbackTid, userId);
            if (originalTransaction == null) {
                logger.error("Original transaction not found for rollback TID: " + rollbackTid);
                return null;
            }
            // Get database connections
            connBet = dbSource.getWriteDB("virtual").getConnection();
            connProfile = dbSource.getWriteDB("main").getConnection();
            connTrxn = dbSource.getWriteDB("trxn").getConnection();
            // Start transactions
            connBet.setAutoCommit(false);
            connProfile.setAutoCommit(false);
            connTrxn.setAutoCommit(false);
            // Determine if original was debit or credit
            int originalType = (Integer) originalTransaction.get("transaction_type_id");
            BigDecimal originalAmount = (BigDecimal) originalTransaction.get("amount");
            boolean wasDebit = (originalType == TransactionConstants.TYPE_DEBIT);
            // For rollback, we reverse the original transaction
            BigDecimal rollbackAmount = originalAmount.negate();
            // Update player balance
            String updateBalanceSql = "UPDATE profile_balance SET balance = balance + ? " +
                    "WHERE profile_id = ? LIMIT 1";

            try (PreparedStatement balanceStmt = connProfile.prepareStatement(updateBalanceSql)) {
                balanceStmt.setBigDecimal(1, rollbackAmount);
                balanceStmt.setString(2, userId);

                int rowsUpdated = balanceStmt.executeUpdate();
                if (rowsUpdated == 0) {
                    logger.error("Failed to update balance for rollback");
                    return null;
                }
            }

            // Create rollback transaction record
            String transactionSql = "INSERT INTO mossbets_transactions.transaction (" +
                    "profile_id, reference_type_id, transaction_type_id, reference_id, " +
                    "amount, currency, source, description, extra_data, created_at) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            long transactionId;
            try (PreparedStatement trxnStmt = connTrxn.prepareStatement(
                    transactionSql, Statement.RETURN_GENERATED_KEYS)) {

                trxnStmt.setString(1, userId);
                trxnStmt.setInt(2, props.evoVoid());
                trxnStmt.setInt(3, wasDebit ? TransactionConstants.TYPE_CREDIT : TransactionConstants.TYPE_DEBIT);
                trxnStmt.setString(4, newTid);
                trxnStmt.setBigDecimal(5, rollbackAmount);
                trxnStmt.setString(6, TransactionConstants.CURRENCY_KES);
                trxnStmt.setString(7, TransactionConstants.SOURCE_SHACKSGAMES_ROLLBACK);
                trxnStmt.setString(8, "Rollback for TID: " + rollbackTid);

                JsonObject rollbackExtraDataBuilder = new JsonObject();
                // Merge extraData into rollbackExtraDataBuilder
                if (extraData != null) {
                    for (String key : extraData.keySet()) {
                        rollbackExtraDataBuilder.add(key, extraData.get(key));
                    }
                }
                rollbackExtraDataBuilder.addProperty("rollback_tid", rollbackTid);
                rollbackExtraDataBuilder.addProperty("original_amount", originalAmount.toString());
                rollbackExtraDataBuilder.addProperty("rollback_type", "transaction_rollback");

                trxnStmt.setString(9, rollbackExtraDataBuilder.toString());

                int affectedRows = trxnStmt.executeUpdate();
                if (affectedRows == 0) {
                    throw new SQLException("Creating rollback transaction failed, no rows affected.");
                }

                try (ResultSet generatedKeys = trxnStmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        transactionId = generatedKeys.getLong(1);
                    } else {
                        throw new SQLException("Creating rollback transaction failed, no ID obtained.");
                    }
                }
            }

            // Update the original bet status if it exists
            if (originalTransaction.get("bet_id") != null) {
                String updateBetSql = "UPDATE mossbets_bets.softgaming_bets SET status = ?, extra_data = ? WHERE bet_id = ?";

                try (PreparedStatement betStmt = connBet.prepareStatement(updateBetSql)) {
                    betStmt.setInt(1, 3); // Status 3 = ROLLED_BACK

                    JsonObject betExtraDataBuilder = new JsonObject();
                    betExtraDataBuilder.addProperty("rollback_tid", rollbackTid);
                    betExtraDataBuilder.addProperty("rollback_transaction_id", transactionId);
                    betExtraDataBuilder.addProperty("rollback_date", Utilities.GetUTCDate("yyyy-MM-dd HH:mm:ss"));
                    betExtraDataBuilder.addProperty("rollback_type", newTid);

                    betStmt.setString(2, betExtraDataBuilder.toString());
                    betStmt.setLong(3, (Long) originalTransaction.get("bet_id"));

                    betStmt.executeUpdate();
                }
            }

            // Commit all transactions
            connBet.commit();
            connTrxn.commit();
            connProfile.commit();

            logger.info("Rollback processed successfully for TID: " + rollbackTid +
                    ", New TID: " + newTid + ", Amount: " + rollbackAmount);

            return String.valueOf(transactionId);

        } catch (SQLException e) {
            // Rollback all transactions in case of error
            try {
                if (connProfile != null) connProfile.rollback();
                if (connTrxn != null) connTrxn.rollback();
                if (connBet != null) connBet.rollback();
            } catch (SQLException ex) {
                logger.error("Error during rollback", ex);
            }
            logger.error("Error processing rollback for TID: " + rollbackTid, e);
            return null;
        } finally {
            // Close connections
            try {
                if (connBet != null) connBet.close();
                if (connTrxn != null) connTrxn.close();
                if (connProfile != null) connProfile.close();
            } catch (SQLException e) {
                logger.error("Error closing database connections", e);
            }
        }
    }

    /**
     * Processes cancellation rollback (subtype="cancel")
     * This type of rollback always credits the player
     *
     * @param tid Transaction ID for the cancellation
     * @param userId User ID
     * @param gameId Game ID
     * @param actionId Action ID
     * @param amount Amount to credit back to player
     * @param extraData Additional data for the cancellation transaction
     * @return Transaction ID if successful, null otherwise
     */

    @WithSpan
    public String processCancellation(String type, String userId, String reference, String roundId,
                                      BigDecimal amount, JsonObject extraData) {

        Connection connBet = null;
        Connection connProfile = null;
        Connection connTrxn = null;
        logger.info("Cancellation: " + type  + ", User ID: " + userId +  "Amount: " + amount
                + ", Extra Data: " + extraData);
        try {
            // Get database connections
            connBet = dbSource.getWriteDB("virtual").getConnection();
            connProfile = dbSource.getWriteDB("main").getConnection();
            connTrxn = dbSource.getWriteDB("trxn").getConnection();

            // Start transactions
            connBet.setAutoCommit(false);
            connProfile.setAutoCommit(false);
            connTrxn.setAutoCommit(false);
            String updateBalanceSql = "";
            if (type.equals("debit")){
                 updateBalanceSql = "UPDATE profile_balance SET balance = balance - ? " +
                        "WHERE profile_id = ? LIMIT 1";
            } else {
                 updateBalanceSql = "UPDATE profile_balance SET balance = balance + ? " +
                        "WHERE profile_id = ? LIMIT 1";
            }
            // For cancellation, always credit the player (positive amount)

            try (PreparedStatement balanceStmt = connProfile.prepareStatement(updateBalanceSql)) {
                balanceStmt.setBigDecimal(1, amount);
                balanceStmt.setString(2, userId);

                int rowsUpdated = balanceStmt.executeUpdate();
                if (rowsUpdated == 0) {
                    logger.error("Failed to update balance for cancellation");
                    return null;
                }
            }

            // Create cancellation transaction record
            String transactionSql = "INSERT INTO mossbets_transactions.transaction (" +
                    "profile_id, reference_type_id, transaction_type_id, reference_id, " +
                    "amount, currency, source, description, created_at) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            long transactionId;
            try (PreparedStatement trxnStmt = connTrxn.prepareStatement(
                    transactionSql, Statement.RETURN_GENERATED_KEYS)) {

                trxnStmt.setString(1, userId);
                trxnStmt.setInt(2, props.evoRefunds());
                trxnStmt.setInt(3, TransactionConstants.TYPE_CREDIT);
                trxnStmt.setString(4, reference);
                trxnStmt.setBigDecimal(5, amount);
                trxnStmt.setString(6, TransactionConstants.CURRENCY_KES);
                trxnStmt.setString(7, TransactionConstants.SOURCE_SHACKSGAMES_CANCEL);
                trxnStmt.setString(8, "Game server cancellation for action: " + roundId);

                JsonObject cancellationExtraDataBuilder = new JsonObject();
                if (extraData != null) {
                    // Merge extraData into cancellationExtraDataBuilder
                    for (String key : extraData.keySet()) {
                        cancellationExtraDataBuilder.add(key, extraData.get(key));
                    }
                }
                cancellationExtraDataBuilder.addProperty("cancellation_type", "game_server_cancel");
                cancellationExtraDataBuilder.addProperty("subtype", "cancel");
                cancellationExtraDataBuilder.addProperty("original_action_id", roundId);

                trxnStmt.setString(9, cancellationExtraDataBuilder.toString());

                int affectedRows = trxnStmt.executeUpdate();
                if (affectedRows == 0) {
                    throw new SQLException("Creating cancellation transaction failed, no rows affected.");
                }

                try (ResultSet generatedKeys = trxnStmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        transactionId = generatedKeys.getLong(1);
                    } else {
                        throw new SQLException("Creating cancellation transaction failed, no ID obtained.");
                    }
                }
            }

            // Find and update related bet if it exists
            String findBetSql = "SELECT bet_id FROM mossbets_bets.virtuals_bet WHERE profile_id = ? AND game_id = ? " +
                    "AND JSON_UNQUOTE(JSON_EXTRACT(extra_data, '$.action_id')) = ? LIMIT 1";

            try (PreparedStatement findStmt = connBet.prepareStatement(findBetSql)) {
                findStmt.setString(1, userId);
                findStmt.setString(2, reference);
                findStmt.setString(3, roundId);

                try (ResultSet rs = findStmt.executeQuery()) {
                    if (rs.next()) {
                        long betId = rs.getLong("bet_id");

                        // Update bet status to cancelled
                        String updateBetSql = "UPDATE mossbets_bets.virtuals_bet SET status = ?, extra_data = ? WHERE bet_id = ?";

                        try (PreparedStatement betStmt = connBet.prepareStatement(updateBetSql)) {
                            betStmt.setInt(1, TransactionConstants.Status.CANCELLED);

                            JsonObject betExtraDataBuilder = new JsonObject();
                            betExtraDataBuilder.addProperty("cancellation_tid", reference);
                            betExtraDataBuilder.addProperty("cancellation_transaction_id", transactionId);
                            betExtraDataBuilder.addProperty("cancellation_date", Utilities.GetUTCDate("yyyy-MM-dd HH:mm:ss"));
                            betExtraDataBuilder.addProperty("cancellation_reason", "Game server cancellation");
                            betExtraDataBuilder.addProperty("cancellation_type", reference);

                            betStmt.setString(2, betExtraDataBuilder.toString());
                            betStmt.setLong(3, betId);

                            betStmt.executeUpdate();
                        }
                    }
                }
            }

            // Commit all transactions
            connBet.commit();
            connTrxn.commit();
            connProfile.commit();

            logger.info("Cancellation processed successfully for TID: " + reference +
                    ", Round ID: " + roundId + ", Amount: " + amount);

            return String.valueOf(transactionId);

        } catch (SQLException e) {
            // Rollback all transactions in case of error
            try {
                if (connProfile != null) connProfile.rollback();
                if (connTrxn != null) connTrxn.rollback();
                if (connBet != null) connBet.rollback();
            } catch (SQLException ex) {
                logger.error("Error during rollback", ex);
            }
            logger.error("Error processing cancellation for TID: " + reference, e);
            return null;
        } finally {
            // Close connections
            try {
                if (connBet != null) connBet.close();
                if (connTrxn != null) connTrxn.close();
                if (connProfile != null) connProfile.close();
            } catch (SQLException e) {
                logger.error("Error closing database connections", e);
            }
        }
    }

    /**
     * CalculateWitholdingTax
     *
     * @param amountWon
     * @param stakeAmount
     * @param taxValue
     * @param taxationType
     * @return
     */

    public static Map<String, Double> CalculateWitholdingTax(Double amountWon,
                                                             Double stakeAmount, Double taxValue, String taxationType) {

        Double possibleWin = (amountWon - stakeAmount);
        Double possibleWinAfter = (possibleWin - ((possibleWin * 100) / (100 + taxValue)));
        if (Utilities.containStr(new String[]{"DIRECT", "TYPE_1"}, taxationType.toUpperCase())) {
            possibleWinAfter = (possibleWin * (taxValue / 100));
        }

        Map<String, Double> whtax = new HashMap<>();
        whtax.put("posibleWin", (amountWon - possibleWinAfter));
        whtax.put("witholdingTax", possibleWinAfter);

        return whtax;
    }

    /**
     * CalculateExciseTax
     *
     * @param stakeAmount
     * @param taxValue
     * @param taxationType
     * @return
     */
    public static Map<String, Double> CalculateExciseTax(Double stakeAmount,
                                                         Double taxValue, String taxationType) {
        Double stakeTax = stakeAmount - ((stakeAmount * 100) / (100 + taxValue));
        if (Utilities.containStr(new String[]{"DIRECT"}, taxationType.toUpperCase())) {
            stakeTax = (stakeAmount * (taxValue / 100));
        }

        Map<String, Double> exciseTax = new HashMap<>();
        exciseTax.put("stakeTax", stakeTax);
        exciseTax.put("stakeAfterTax", (stakeAmount - stakeTax));

        return exciseTax;
    }



    /**
     * createSMSOutBox
     *
     * @param profileId
     * @param shortCode
     * @param message
     * @param messageType
     * @param dbConn
     * @return
     */

    @WithSpan
    public  long createSMSOutBox(String profileId, String shortCode, String message,
            String messageType, final AgroalDataSource dbConn) {

        String outboxSql = "INSERT INTO profile_outbox(profile_id,sender_id,message_type"
                + ",message,status,created_at) VALUES(?,?,?,?,?,NOW())";

        long outbox = 0;
        try (final java.sql.Connection dbW = dbConn.getConnection();
                final PreparedStatement ps = dbW.prepareStatement(
                        outboxSql, PreparedStatement.RETURN_GENERATED_KEYS);) {
            dbW.setAutoCommit(false);
            ps.setString(1, profileId);
            ps.setString(2, shortCode);
            ps.setString(3, messageType);
            ps.setString(4, message);
            ps.setInt(5, 1);
            outbox = ps.executeUpdate();
            try (ResultSet rs = ps.getGeneratedKeys()) {
                if (rs.next()) {
                    outbox = rs.getLong(1);
                }
            }

            if (outbox < 1) {
                dbW.rollback();
                throw new SQLException(messageType + " Sms Outbox for profileId:" + profileId);
            }
            dbW.commit();
        } catch (SQLException e) {
        }

        return outbox;
    }

    public  List<String> storeGamesInDatabase(String jsonResponse) {
       
        List<String> insertedGames = new ArrayList<>();      
        try {
            JsonObject responseObj = JsonParser.parseString(jsonResponse).getAsJsonObject();
            if (!responseObj.has("data")) {
                return insertedGames; 
            }
            
            JsonArray games = responseObj.getAsJsonArray("data");
            
            for (int i = 0; i < games.size(); i++) {
                JsonObject game = games.get(i).getAsJsonObject();
                String gameId = game.get("gameId").getAsString();
                String gameName = gameId.toUpperCase(); 
                String gameType = game.get("gameType").getAsString();
                String url = game.get("url").getAsString();
                boolean isActive = game.get("isActive").getAsBoolean();
            
                JsonObject assets = game.getAsJsonObject("assets");
                String logo = assets.has("logo") ? assets.get("logo").getAsString() : null;
                String banner = assets.has("banner") ? assets.get("banner").getAsString() : logo;
            
                JsonObject extra_data = new JsonObject();
                extra_data.add("assets", assets); 
                extra_data.addProperty("url", url);
                extra_data.addProperty("isActive", isActive);
                
                String checkSql = "SELECT game_name FROM mossbets_bets.games WHERE game_name = ?";
                String insertSql = "INSERT INTO mossbets_bets.games " +
                        "(game_name, provider, provider_game_id, description, ip_address, image_url, game_id, status, created_at, updated_at, extra_data) " +
                        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?)";
                try (Connection conn = dbSource.getWriteDB("virtual").getConnection();
                     PreparedStatement checkStmt = conn.prepareStatement(checkSql)) {
                    checkStmt.setString(1, gameName);
                    ResultSet rs = checkStmt.executeQuery();
                    if (!rs.next()) {
                        try (PreparedStatement insertStmt = conn.prepareStatement(insertSql)) {
                            int j = 1;
                            insertStmt.setString(j++, gameName);                        // game_name
                            insertStmt.setString(j++, "SHACK-EVOLUTION");              // provider
                            insertStmt.setString(j++, gameId);                         // provider_game_id
                            insertStmt.setString(j++, gameType);                       // description
                            insertStmt.setString(j++, "0.0.0.0");                      // ip_address
                            insertStmt.setString(j++, banner);                         // image_url
                            insertStmt.setString(j++, gameId);                         // game_id
                            insertStmt.setInt(j++, 1);                                 // status
                            insertStmt.setString(j++, extra_data.toString());         // extra_dat
                            insertStmt.executeUpdate();
                            insertedGames.add(gameName);
                        }
                    }
                } catch (Exception e) {
                    logger.error("Error storing game '" + gameName + "' in database: " + e.getMessage(), e);
                }
            }
            
            
        } catch (Exception e) {
            logger.error("Error storing games in database: " + e.getMessage(), e);
        }
        return insertedGames;
    }
    public String formatBalanceToTwoDecimals(String newBalance) {
        try {
            double balanceValue = Double.parseDouble(newBalance);
            return String.format("%.2f", balanceValue);
        } catch (NumberFormatException e) {
            return "0.00";
        }
    }
}