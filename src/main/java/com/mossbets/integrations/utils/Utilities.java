package com.mossbets.integrations.utils;

import com.mossbets.integrations.controllers.resources.DB;
import com.mossbets.integrations.models.ApiCallResult;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.HttpHeaders;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.Socket;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;

import org.apache.commons.lang3.StringUtils;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import io.netty.util.internal.ThreadLocalRandom;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class Utilities {
    @Inject
    public DB dbSource;

    @Inject
    Logger logger;

    /**
     * GetLicense
     *
     * @return
     */
    public static String GetLicense() {
        String license = "[+]**********************************************[+]\n"
                + "[+]**********************************************[+]\n"
                + "[+]**********************************************[+]\n"
                + "[+]********          MossPlay           *********[+]\n"
                + "[+]********                             *********[+]\n"
                + "[+]********          Liden.io           *********[+]\n"
                + "[+]********  AUTHOR: JOSEPHAT MUKUHA    *********[+]\n"
                + "[+]**********************************************[+]\n"
                + "[+]**********************************************[+]\n";

        return license;
    }
     /**
     * Calculates the Time Taken (TAT - Turn Around Time) between the current time and the given start time.
     *
     * @param startTime The start time in milliseconds (usually from System.currentTimeMillis())
     * @return The time difference in milliseconds
     */
    public static Duration calculateTAT(Instant startTime) {
        return Duration.between(startTime, Instant.now());
    }


    public Map<String, Object> authenticateAccessKey(String accessKey) {
        Map<String, Object> response = new HashMap<>();
    
        try (Connection conn = dbSource.getWriteDB("main").getConnection()) {
            String hashedAccessKey = CreateMd5(accessKey);
    
            String sql = "SELECT pl.profile_id, p.msisdn, p.name, p.acc_number, p.hash, p.network " +
                         "FROM profile_login pl " +
                         "JOIN profile p ON pl.profile_id = p.id " +
                         "WHERE pl.access_token = ? " +
                         "AND pl.token_expiry_date >= NOW() " +
                         "AND pl.status = ?";
    
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, hashedAccessKey);
                stmt.setInt(2, 1); // Assuming 1 = Active
    
                try (ResultSet rs = stmt.executeQuery()) {
                    if (!rs.next()) {
                        response.put("code", 401);
                        response.put("message", "Session Expired. Login again!");
                        return response;
                    }
    
                    Map<String, Object> result = new HashMap<>();
                    result.put("profile_id", rs.getLong("profile_id"));
                    result.put("msisdn", rs.getString("msisdn"));
                    result.put("name", rs.getString("name"));
                    result.put("acc_number", rs.getString("acc_number"));
                    result.put("hash", rs.getString("hash"));
                    result.put("network", rs.getString("network"));
    
                    // Blacklist check
                    if (checkBlacklist(result.get("profile_id"))) {
                        response.put("code", 401);
                        response.put("message", "Session Error: Contact customer support for assistance.");
                        return response;
                    }
    
                    // Add fixed fields
                    result.put("currency", "KES");
                    result.put("country", "KENYA");
    
                    response.put("code", 200);
                    response.put("message", "Login Success");
                    response.put("data", result);
                    logger.info("authenticateAccessKey Response: " + response);
                    logger.error("authenticateAccessKey Response: " + response);
                    return response;
                }
            }
    
        } catch (Exception ex) {
            logger.error("authenticateAccessKey Exception: " + ex.getMessage(), ex);
            response.put("code", 500);
            response.put("message", "Session: An Error occurred!");
            return response;
        }
    }

    public static String CreateMd5(String input) throws NoSuchAlgorithmException {

        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(input.getBytes());
        BigInteger number = new BigInteger(1, digest);
        return String.format("%032x", number);
    }


    public boolean checkBlacklist(Object playerId) throws Exception {
        String query = "SELECT id, reason, status, blacklisted_on FROM profile_blacklist WHERE profile_id = ?";
    
        try (Connection conn = dbSource.getWriteDB("main").getConnection();
             PreparedStatement stmt = conn.prepareStatement(query)) {
    
            stmt.setObject(1, playerId);
    
            try (ResultSet rs = stmt.executeQuery()) {
                if (!rs.next()) {
                    return false;
                }
    
                int status = rs.getInt("status");
                if (status == 3) {
                    String reason = rs.getString("reason");
                    String blacklistedOn = rs.getString("blacklisted_on");
    
                    logger.info("checkBlacklist | ProfileId: " + playerId
                            + " | Reason: " + reason
                            + " | BlacklistedOn: " + blacklistedOn);
    
                    return true;
                }
    
                return false;
            }
    
        } catch (Exception e) {
            logger.error("checkBlacklist Exception for playerId: " + playerId, e);
            throw e;
        }
    }
    
    
    


    /**
         * Retrieves the client's IP address from HTTP headers.
         * Checks common proxy headers in order of preference.
         *
         * @return The client's IP address or "0.0.0.0" if not found
         */
    public static String getClientIpAddress() {
            // jakarta.enterprise.inject.spi.CDI<Object> current = jakarta.enterprise.inject.spi.CDI.current();
            // jakarta.ws.rs.core.HttpHeaders headers = null;

            // try {
            //     headers = current.select(jakarta.ws.rs.core.HttpHeaders.class).get();
            // } catch (Exception e) {
            //     logger.warn("Could not get HttpHeaders: " + e.getMessage());
            //     return "0.0.0.0";
            // }

            // if (headers == null) {
            //     return "0.0.0.0";
            // }

            // // List of headers to check (in order of preference)
            // String[] headerNames = {
            //     "X-Forwarded-For",
            //     "Proxy-Client-IP",
            //     "WL-Proxy-Client-IP",
            //     "HTTP_X_FORWARDED_FOR",
            //     "HTTP_X_FORWARDED",
            //     "HTTP_X_CLUSTER_CLIENT_IP",
            //     "HTTP_CLIENT_IP",
            //     "HTTP_FORWARDED_FOR",
            //     "HTTP_FORWARDED",
            //     "HTTP_VIA",
            //     "REMOTE_ADDR"
            // };

            // // Check each header
            // for (String header : headerNames) {
            //     String ip = headers.getHeaderString(header);
            //     if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            //         // Handle multiple IPs (X-Forwarded-For can contain multiple IPs)
            //         if (ip.contains(",")) {
            //             String[] ips = ip.split(",");
            //             ip = ips[0].trim();
            //         }
            //         return ip;
            //     }
            // }

            // // If no IP found in headers, try to get from request context
            // try {
            //     jakarta.ws.rs.core.SecurityContext securityContext = current.select(jakarta.ws.rs.core.SecurityContext.class).get();
            //     if (securityContext != null && securityContext instanceof jakarta.ws.rs.core.SecurityContext) {
            //         jakarta.ws.rs.core.SecurityContext sc = (jakarta.ws.rs.core.SecurityContext) securityContext;
            //         if (sc.getUserPrincipal() != null) {
            //             return sc.getUserPrincipal().getName();
            //         }
            //     }
            // } catch (Exception e) {
            //     logger.warn("Could not get IP from security context: " + e.getMessage());
            // }

            return "0.0.0.0";
        }

    /**
     * GetUTCDate
     *
     * @param format
     * @return
     */
    public static String GetUTCDate(String format) {

        if ((null == format) || isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss.SSSZ";
        }

        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date now = new Date();

        return sdf.format(now);
    }

    /**
     * parseRequest
     *
     * @param request
     * @return
     */
    public static JsonObject parseRequest(String request) {
        try {
            if (request == null || request.trim().isEmpty()) {
                System.err.println("parseRequest: Request is null or empty");
                return null;
            }
            return JsonParser.parseString(request).getAsJsonObject();
        } catch (JsonSyntaxException e) {
            System.err.println("parseRequest: JsonSyntaxException - " + e.getMessage());
            System.err.println("parseRequest: Request content: " + request);
            return null;
        }
    }

    /**
     * safeGetLong
     *
     * @param obj
     * @param key
     * @param defaultValue
     * @return
     */
    public static long validateLongJson(JSONObject obj, String key, long defaultValue) {
        if (!obj.has(key) || obj.isNull(key)) {
            return defaultValue;
        }

        Object raw = obj.opt(key);

        if (raw instanceof Number) {
            return ((Number) raw).longValue();
        }

        if (raw instanceof String) {
            String s = ((String) raw).trim();
            if (!s.isEmpty()) {
                try {
                    return Long.parseLong(s);
                } catch (NumberFormatException e) {
                    // fall through to return default
                }
            }
        }

        // anything else (empty string, boolean, object, etc.)
        return defaultValue;
    }

    /**
     * validateHeaders
     *
     * @param headers
     * @return
     */
    public static Map<String, String> validateHeaders(HttpHeaders headers) {
        Map<String, String> values = new HashMap<>();
        values.put("x-api-key", headers.getHeaderString("x-api-key"));
        values.put("x-app-key", headers.getHeaderString("x-app-key"));
        values.put("x-hash-key", headers.getHeaderString("x-hash-key"));
        values.put("x-ua", headers.getHeaderString("x-ua"));
        values.put("timezone", headers.getHeaderString("timezone"));
        for (Map.Entry<String, String> entry : values.entrySet()) {
            if (Utilities.isBlank(entry.getValue())
                    && !entry.getKey().equals("x-ua")
                    && !entry.getKey().equals("timezone")) {
                return null;
            }
        }
        return values;
    }

    /**
     * bindParams
     *
     * @param ps
     * @param params
     * @throws SQLException
     */
    public static void bindParams(PreparedStatement ps, Object[] params) throws SQLException {
        if (params == null || ps == null) {
            return;
        }

        for (int i = 0; i < params.length; i++) {
            int index = i + 1;
            Object param = params[i];

            if (param == null) {
                ps.setObject(index, null);
                continue;
            }

            switch (param.getClass().getSimpleName()) {
                case "String" ->
                    ps.setString(index, (String) param);
                case "Integer" ->
                    ps.setInt(index, (Integer) param);
                case "Long" ->
                    ps.setLong(index, (Long) param);
                case "Float" ->
                    ps.setFloat(index, (Float) param);
                case "Double" ->
                    ps.setDouble(index, (Double) param);
                case "Boolean" ->
                    ps.setBoolean(index, (Boolean) param);
                default ->
                    ps.setObject(index, param); // fallback for other types
            }
        }
    }

    /**
     * isHttpSuccess
     *
     * @param httpResults
     * @param successHttpCodes
     * @return
     */
    public static boolean isHttpSuccess(ApiCallResult httpResults, Set<Integer> successHttpCodes) {
        return successHttpCodes.contains(httpResults.getHttpCode())
                && successHttpCodes.contains(httpResults.getStatus_code())
                && "Success".equalsIgnoreCase(httpResults.getStatus_desc());
    }

    /**
     * SequentialMask
     * @param inputStr
     * @param maskPercentage
     * @param maskChar
     * @return
     */
    public static String sequentialMask(String inputStr, double maskPercentage, char maskChar) {
        int length = inputStr.length();
        int maskCount = (int) Math.ceil((length - 2) * maskPercentage);
        StringBuilder masked = new StringBuilder(inputStr);

        for (int i = 1; i <= maskCount; i++) {
            masked.setCharAt(i, maskChar);
        }

        return masked.toString();
    }
    /**
     * PingServer
     *
     * @param host
     * @param portNumber
     * @param timeout
     * @return
     */
    public static int PingServer(final String host, final int portNumber, final int timeout) {
        int state = 3;

        try (Socket ps = new Socket(host, portNumber);) {
            ps.setSoTimeout(timeout);

            if (ps.isConnected()) {
                state = 0;
            }
        } catch (Exception ex) {
        }

        return state;
    }

    /**
     * Round2Decimal
     *
     * @param input
     * @return
     */
    public static double Round2Decimal(double input) {
        return (Math.round(input * 100.00) / 100.00);
    }

    /**
     * ReferenceNumber
     *
     * @param uniqueId
     * @return
     */
    public static String ReferenceNumber(boolean uniqueId) {
        StringBuilder referenceId = new StringBuilder();

        Map<String, String> year = new HashMap<>();
        year.put("2022", "Z");
        year.put("2023", "Y");
        year.put("2024", "X");
        year.put("2025", "W");
        year.put("2026", "V");
        year.put("2027", "U");
        year.put("2028", "T");
        year.put("2029", "S");
        year.put("2030", "R");
        year.put("2031", "Q");
        year.put("2032", "O");
        year.put("2033", "N");
        referenceId.append(year.get(now("yyyy")));

        String[] month = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L"};
        referenceId.append(month[Integer.parseInt(now("MM")) - 1]);

        int d = Integer.parseInt(now("dd"));
        if (d >= 1 && d <= 10) {
            referenceId.append(d);
        } else {
            referenceId.append(num2alpha(d));
        }

        referenceId.append(num2alpha(Integer.parseInt(now("HH"))));
        referenceId.append(GenerateRandStr((14 - (referenceId.length())), 1));

        if (uniqueId) {
            referenceId.append('-').append(GenerateRandStr(3, 1));
        }

        return referenceId.toString().toUpperCase();
    }

    private static String num2alpha(int n) {
        StringBuilder r = new StringBuilder();
        for (int i = 1; n >= 0 && i < 10; i++) {
            r.insert(0, (char) (0x41 + (n % (int) Math.pow(26, i) / (int) Math.pow(26, i - 1))));
            n -= (int) Math.pow(26, i);
        }
        return r.toString();
    }

    /**
     * RandomDecimal
     *
     * @param min
     * @param max
     * @param digit
     * @return
     */
    public static double RandomDecimal(double min, double max, int digit) {
        return ThreadLocalRandom.current().nextInt((int) (min * Math.pow(10, digit)), (int) (max * Math.pow(10, digit) + 1)) / Math.pow(10, digit);
    }

    /**
     * RandomDecimal
     *
     * @param min
     * @param max
     * @return
     */
    public static double RandomDecimal(double min, double max) {
        return RandomDecimal(min, max, 2);
    }

    /**
     * GenerateRandStr
     *
     * @param length
     * @param type
     * @return
     */
    public static String GenerateRandStr(int length, int type) {
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789#&*%";

        if (type > 0) {
            chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        }

        List<Character> charList = chars.chars()
                .mapToObj(e -> (char) e)
                .collect(Collectors.toList());

        Collections.shuffle(charList);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(charList.get(i));
        }

        return sb.toString();
    }

    /**
     * findInIntegerArray
     *
     * @param array
     * @param y
     * @return
     */
    public static boolean findInIntegerArray(int[] array, int y) {
        if ((null == array) || array.length == 0) {
            return false;
        }

        for (int value : array) {
            if (value == y) {
                return true;
            }
        }

        return false;
    }

    /**
     * containStr
     *
     * @param array
     * @param target
     * @return
     */
    public static boolean containStr(String[] array, String target) {
        if (array == null || target == null) {
            return false;
        }

        for (String value : array) {
            if (target.equalsIgnoreCase(value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * GetRandomInt
     *
     * @param min
     * @param max
     * @return
     */
    public static int GetRandomInt(int min, int max) {
        return min + (int) (Math.random() * ((max - min) + 1));
    }

    /**
     * toSHA1
     *
     * @param password
     * @return
     */
    public static String toSHA1(String password) {
        String sha1 = "";
        try {
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(password.getBytes("UTF-8"));
            sha1 = byteToHex(crypt.digest());
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
        }
        return sha1;
    }

    /**
     * byteToHex
     *
     * @param hash
     * @return
     */
    public static String byteToHex(final byte[] hash) {
        String result;
        try (Formatter formatter = new Formatter()) {
            for (byte b : hash) {
                formatter.format("%02x", b);
            }
            result = formatter.toString();
        }
        return result;
    }

    // /**
    //  * CreateMd5
    //  *
    //  * @param input
    //  * @return
    //  * @throws NoSuchAlgorithmException
    //  */
    // public static String CreateMd5(String input) throws NoSuchAlgorithmException {
    //     MessageDigest md = MessageDigest.getInstance("MD5");
    //     byte[] messageDigest = md.digest(input.getBytes());
    //     BigInteger no = new BigInteger(1, messageDigest);
    //     String hashtext = no.toString(16);
    //     while (hashtext.length() < 32) {
    //         hashtext = "0" + hashtext;
    //     }
    //     return hashtext;
    // }

    public static long CalculateTAT(Instant start) {
        return Duration.between(start, Instant.now()).toMillis();
    }

    /**
     * CleanCallbackUrl
     *
     * @param Url
     * @return
     */
    public static String CleanCallbackUrl(String Url) {
        if (Url == null || Url.isEmpty()) {
            return Url; // Handle empty or null strings
        }

        int length = Url.length();
        return length > 1
                && Url.charAt(length - 1) == '/'
                ? Url.substring(0, length - 1)
                : Url;
    }

    /**
     *
     * @param value
     * @return
     */
    @SuppressWarnings("null")
    public static boolean isBlank(String value) {
        if (null == value) {
            return true;
        }

        return (null == value)
                || StringUtils.isBlank(value)
                || (StringUtils.length(value) < 1)
                || "null".equalsIgnoreCase(value)
                || "false".equalsIgnoreCase(value);
    }

    /**
     * isNumeric
     *
     * @param strNum
     * @return
     */
    public static boolean isNumeric(String strNum) {
        try {
            double d = Double.parseDouble(strNum);
        } catch (NumberFormatException | NullPointerException nfe) {
            return false;
        }
        return true;
    }

    /**
     * JsonValidator
     *
     * @param JsonStr
     * @param isArray
     * @return
     */
    public static boolean JsonValidator(String JsonStr, Boolean isArray) {

        if (isArray == true) {
            try {
                JsonParser.parseString(JsonStr).getAsJsonArray();
                return true;
            } catch (JsonSyntaxException jse) {
                return false;
            } catch (Exception e) {
                return false;
            }
        }

        try {
            JsonParser.parseString(JsonStr).getAsJsonObject();
            return true;
        } catch (JsonSyntaxException ex) {
            return false;
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * ValidateUrl
     *
     * @param url
     * @return
     */
    public static boolean ValidateUrl(String url) {
        String urlRegex = "^(http|https)://[-a-zA-Z0-9+&@#/%?=~_|,!:.;]*[-a-zA-Z0-9+@#/%=&_|]";
        Pattern pattern = Pattern.compile(urlRegex);
        Matcher m = pattern.matcher(url);
        return m.matches();
    }

    /**
     * isValidDateFormat
     *
     * @param dateStr
     * @return
     */
    public static boolean isValidDateFormat(String dateStr) {
        if (Utilities.isBlank(dateStr)) {
            return false;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            LocalDate.parse(dateStr, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     *
     * @param format
     * @return
     */
    public static String now(String format) {
        return now(format, null);
    }

    /**
     *
     * @param format
     * @param timestamp
     * @return
     */
    public static String now(String format, Long timestamp) {
        if (timestamp == null) {
            timestamp = System.currentTimeMillis() / 1000; // Convert to seconds
        }

        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        return sdf.format(new Date(timestamp * 1000)); // Convert to milliseconds
    }

    /**
     * getLogPreString
     *
     * @param appName
     * @return
     */
    public static String getLogPreString(String appName) {
        return appName + " | ";
    }

   

    

   

   

    /**
     * Base64 URL-safe encoding
     */
    public static String base64urlEncode(byte[] data) {
        return Base64.getEncoder().encodeToString(data)
                .replace('+', '-')
                .replace('/', '_')
                .replace("=", "");
    }

    /**
     * Base64 URL-safe decoding
     */
    public static byte[] base64urlDecode(String str) {
        String base64 = str.replace('-', '+').replace('_', '/');
        return Base64.getDecoder().decode(base64);
    }

    /**
     * Encrypt data using AES-256-CBC
     */
    public static String encrypt(String clearText, boolean urlSafe, String secretKey) throws Exception {
        try {
            String encryptionKey = Base64.getEncoder().encodeToString(secretKey.getBytes());
            
            SecureRandom random = new SecureRandom();
            byte[] iv = new byte[16];
            random.nextBytes(iv);
            
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(Base64.getDecoder().decode(encryptionKey), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encrypted = cipher.doFinal(clearText.getBytes());
            
            String encryptedText = Base64.getEncoder().encodeToString(encrypted);
            String data = Base64.getEncoder().encodeToString((encryptedText + "::" + Base64.getEncoder().encodeToString(iv)).getBytes());
            
            return urlSafe ? base64urlEncode(data.getBytes()) : data;
        } catch (Exception ex) {
            throw new Exception("Encryption failed: " + ex.getMessage(), ex);
        }
    }

    /**
     * Encrypt with default secret key
     */
    public static String encrypt(String clearText) throws Exception {
        return encrypt(clearText, false, "defaultSecretKey"); // Replace with actual default key
    }

    /**
     * Decrypt data using AES-256-CBC
     */
    public static String decrypt(String cipherData, boolean urlSafe, String secretKey) throws Exception {
        try {
            if (cipherData == null || cipherData.isEmpty()) {
                throw new Exception("Empty cipher string");
            }
            
            String data = cipherData;
            if (urlSafe) {
                data = new String(base64urlDecode(cipherData));
            }
            
            String encryptionKey = Base64.getEncoder().encodeToString(secretKey.getBytes());
            byte[] decoded = Base64.getDecoder().decode(data);
            String decodedStr = new String(decoded);
            
            String[] parts = decodedStr.split("::", 2);
            if (parts.length != 2) {
                throw new Exception("Malformed encrypted data structure");
            }
            
            byte[] encryptedData = Base64.getDecoder().decode(parts[0]);
            byte[] iv = Base64.getDecoder().decode(parts[1]);
            
            if (iv.length > 16) {
                byte[] truncatedIv = new byte[16];
                System.arraycopy(iv, 0, truncatedIv, 0, 16);
                iv = truncatedIv;
            }
            
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(Base64.getDecoder().decode(encryptionKey), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decrypted = cipher.doFinal(encryptedData);
            
            return new String(decrypted);
        } catch (Exception ex) {
            throw new Exception("Decryption failed: " + ex.getMessage(), ex);
        }
    }

    /**
     * Decrypt with default secret key
     */
    public static String decrypt(String cipherData) throws Exception {
        return decrypt(cipherData, false, "defaultSecretKey"); // Replace with actual default key
    }

}
