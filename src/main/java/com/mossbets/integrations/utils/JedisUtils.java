package com.mossbets.integrations.utils;

import com.mossbets.integrations.utils.props.Props;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.spi.CDI;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.exceptions.JedisConnectionException;
import redis.clients.jedis.exceptions.JedisException;

@ApplicationScoped
public class JedisUtils {

    @Inject
    Logger logger;
    private static final String prefix = "MOSSBETS_";
    /**
     * JedisConn
     *
     * @return
     */
    private static JedisPool createJedisPool() {
        Props props = CDI.current().select(Props.class).get();
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(50); // Increased for higher concurrency
        config.setMaxIdle(props.redisMaxIdle());
        config.setMinIdle(props.redisMinIdle());
        config.setTestWhileIdle(true);
        config.setTestOnBorrow(true);
        config.setTestOnReturn(true);
        config.setLifo(false);
        //config.setMaxWaitMillis(500); // 0.5-second wait for connections
        return new JedisPool(
                config,
                props.redisHost(),
                props.redisPort(),
                props.redisTimeOut(),
                props.redisAuth());
    }

    /**
     * FetchOneData
     *
     * @param key
     * @return
     */
    public static String fetchOneData(String key) {
        String dataObj = "";
        try {
            JedisPool jedisPool = createJedisPool();
            try (Jedis jedis = jedisPool.getResource()) {
                dataObj = jedis.get(prefix.concat(key));
                jedisPool.close();
                if (Utilities.isBlank(dataObj)) {
                    dataObj = "";
                }
            } catch (JedisConnectionException ex) {
                logger.error("Redis connection failed: " + ex.getMessage());
            } catch (JedisException ex) {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            } finally {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            }
        } catch (Exception ex) {
            logger.error(Utilities.getLogPreString("JedisUtils")
                    + "JedisUtils.fetchOneData()"
                    + "| Key:" + key
                    + "| Exception: " + ex.getMessage());
        }
        return dataObj;
    }

    /**
     * deleteData
     *
     * @param key
     * @return
     */
    public static boolean deleteData(String key) {
        boolean processed = false;
        String fullKey = prefix.concat(key);
        JedisPool jedisPool = createJedisPool();
        try (Jedis jedis = jedisPool.getResource()) {
            Long deleted = jedis.del(fullKey);
            processed = deleted > 0;
        } catch (JedisConnectionException e) {
            // handle gracefully
            logger.error("Redis connection failed: {}" + e.getMessage());
        } catch (Exception ex) {
            logger.error(Utilities.getLogPreString("JedisUtils")
                    + "JedisUtils.deleteData()"
                    + "| Key:" + key
                    + "| Exception: " + ex.getMessage());
        }
        return processed;
    }

    /**
     * InsertData
     *
     * @param key
     * @param dataObj
     * @param timeout
     * @return
     */
    public static boolean saveData(String key, String dataObj, int timeout) {
        boolean processed = false;
        String fullKey = prefix.concat(key);
        JedisPool jedisPool = createJedisPool();
        try (Jedis jedis = jedisPool.getResource()) {
            if (timeout > 0) {
                jedis.setex(fullKey, timeout, dataObj);
            } else {
                jedis.set(fullKey, dataObj);
            }
            jedisPool.close();
            processed = true;
        } catch (JedisConnectionException ex) {
            logger.error("Redis connection failed: " + ex.getMessage());
        } catch (JedisException ex) {
            if (null != jedisPool) {
                jedisPool.close();
            }
        }
        return processed;
    }
}
