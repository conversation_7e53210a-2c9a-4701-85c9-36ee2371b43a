package com.mossbets.integrations.utils.crypto;

import com.google.gson.Gson;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;
import java.nio.charset.StandardCharsets;
import java.util.*;

import io.opentelemetry.instrumentation.annotations.WithSpan;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;
import com.google.gson.JsonObject;


@ApplicationScoped
public class HmacService {
    @Inject
    Logger logger;
    @WithSpan
    public boolean validateHmac(String hmacSecret, JsonObject m) throws Exception {
        if (m == null) {
            throw new Exception("Request is null");
        }
        Map<String, Object> request = jsonObjectToMap(m);
        String calculatedHmac = generateFundistHmac(request, hmacSecret);
        if ((m.has("hmac")) && (calculatedHmac.equals(m.get("hmac").getAsString()))) {
            return true;
        } else if (!calculatedHmac.equals(m.get("hmac").getAsString())) {
            throw new Exception("Invalid HMAC signature");
        } else if (!m.has("hmac")) {
            throw new Exception("Missing required field: hmac");
        }
        return false;
    }

    public static JsonObject stringToJsonObject(String jsonInput) throws Exception {
        try {
            return JsonParser.parseString(jsonInput).getAsJsonObject();
        } catch (JsonSyntaxException e) {
            throw new Exception("Failed to parse JSON: " + e.getMessage(), e);
        }
    }

    public static String generateFundistHmac(Map<String, Object> message, String secret) throws Exception {
        // SHA-256 hash of secret
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] secretHash = digest.digest(secret.getBytes(StandardCharsets.UTF_8));

        // Copy and clean message
        Map<String, Object> messageCopy = new HashMap<>(message);
        messageCopy.remove("hmac");

        // Sort keys
        Map<String, Object> sortedMessage = new TreeMap<>(messageCopy);

        // Normalize values
        for (Map.Entry<String, Object> entry : sortedMessage.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof Boolean) {
                entry.setValue((Boolean) value ? "1" : "");
            } else if (value instanceof List) {
                entry.setValue(processArray((List<?>) value));
            } else if (value instanceof Map) {
                entry.setValue(processObject((Map<?, ?>) value));
            }
        }

        // Concatenate values
        StringBuilder hmacBase = new StringBuilder();
        for (Object value : sortedMessage.values()) {
            hmacBase.append(value);
        }

        // HMAC-SHA256
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        SecretKeySpec keySpec = new SecretKeySpec(secretHash, "HmacSHA256");
        hmacSha256.init(keySpec);

        byte[] rawHmac = hmacSha256.doFinal(hmacBase.toString().getBytes(StandardCharsets.UTF_8));
        return bytesToHex(rawHmac);
    }

    private static String processArray(List<?> list) {
        StringBuilder sb = new StringBuilder();
        for (Object item : list) {
            if (item instanceof Map) {
                sb.append(processObject((Map<?, ?>) item));
            } else {
                sb.append(item.toString());
            }
        }
        return sb.toString();
    }

    private static String processObject(Map<?, ?> map) {
        Map<String, Object> sortedMap = new TreeMap<>();
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            sortedMap.put(entry.getKey().toString(), entry.getValue());
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedMap.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof Boolean) {
                sb.append((Boolean) value ? "1" : "");
            } else if (value instanceof List) {
                sb.append(processArray((List<?>) value));
            } else if (value instanceof Map) {
                sb.append(processObject((Map<?, ?>) value));
            } else {
                sb.append(value != null ? value.toString() : "");
            }
        }
        return sb.toString();
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            hexString.append(String.format("%02x", b & 0xff));
        }
        return hexString.toString();
    }
    public static Map<String, Object> jsonObjectToMap(JsonObject jsonObject) {
        try {
            Gson gson = new Gson();
            return gson.fromJson(jsonObject, Map.class);
        } catch (com.google.gson.JsonSyntaxException e) {
            throw new IllegalArgumentException("Invalid JSON string", e);
        }
    }
    public static String stripFirstChar(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        return input.substring(1);
    }

}