#Datasource MASTER

#Main
quarkus.datasource."db1".db-kind=mysql
quarkus.datasource."db1".username=${DB_USERNAME}
quarkus.datasource."db1".password=${DB_PASSWORD}
quarkus.datasource."db1".jdbc.initial-size=10
quarkus.datasource."db1".jdbc.max-size=50
quarkus.datasource."db1".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db1".jdbc.url=jdbc:mysql://${DB_WRITE}:3306/mossbets_profile?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."db1".jdbc.validation-query-sql=select 1
quarkus.datasource."db1".jdbc.pooling-enabled=true
quarkus.datasource."db1".jdbc.min-size=1
quarkus.datasource."db1".jdbc.idle-removal-interval=60S
quarkus.datasource."db1".jdbc.background-validation-interval=10
quarkus.datasource."db1".jdbc.leak-detection-interval=30
quarkus.datasource."db1".jdbc.acquisition-timeout=10
quarkus.datasource."db1".jdbc.max-lifetime=1800S

#Bets
quarkus.datasource."db3".db-kind=mysql
quarkus.datasource."db3".username=${DB_USERNAME}
quarkus.datasource."db3".password=${DB_PASSWORD}
quarkus.datasource."db3".jdbc.initial-size=10
quarkus.datasource."db3".jdbc.max-size=50
quarkus.datasource."db3".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db3".jdbc.url=jdbc:mysql://${DB_WRITE}:3306/mossbets_bets?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."db3".jdbc.validation-query-sql=select 1
quarkus.datasource."db3".jdbc.pooling-enabled=true
quarkus.datasource."db3".jdbc.min-size=1
quarkus.datasource."db3".jdbc.idle-removal-interval=60S
quarkus.datasource."db3".jdbc.background-validation-interval=10
quarkus.datasource."db3".jdbc.leak-detection-interval=30
quarkus.datasource."db3".jdbc.acquisition-timeout=10
quarkus.datasource."db3".jdbc.max-lifetime=1800S

#Transactions
quarkus.datasource."db2".db-kind=mysql
quarkus.datasource."db2".username=${DB_USERNAME}
quarkus.datasource."db2".password=${DB_PASSWORD}
quarkus.datasource."db2".jdbc.initial-size=10
quarkus.datasource."db2".jdbc.max-size=50
quarkus.datasource."db2".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db2".jdbc.url=jdbc:mysql://${DB_WRITE}:3306/mossbets_transactions?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."db2".jdbc.validation-query-sql=select 1
quarkus.datasource."db2".jdbc.pooling-enabled=true
quarkus.datasource."db2".jdbc.min-size=1
quarkus.datasource."db2".jdbc.idle-removal-interval=60S
quarkus.datasource."db2".jdbc.background-validation-interval=10
quarkus.datasource."db2".jdbc.leak-detection-interval=30
quarkus.datasource."db2".jdbc.acquisition-timeout=10
quarkus.datasource."db2".jdbc.max-lifetime=1800S

#Bonus
quarkus.datasource."dbr4".db-kind=mysql
quarkus.datasource."dbr4".username=${DB_USERNAME}
quarkus.datasource."dbr4".password=${DB_PASSWORD}
quarkus.datasource."dbr4".jdbc.initial-size=5
quarkus.datasource."dbr4".jdbc.max-size=100
quarkus.datasource."dbr4".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr4".jdbc.url=jdbc:mysql://${DB_READ}:3306/mossbets_bonus?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr4".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr4".jdbc.pooling-enabled=true
quarkus.datasource."dbr4".jdbc.min-size=1
quarkus.datasource."dbr4".jdbc.idle-removal-interval=50
quarkus.datasource."dbr4".jdbc.background-validation-interval=10
quarkus.datasource."dbr4".jdbc.leak-detection-interval=30
quarkus.datasource."dbr4".jdbc.acquisition-timeout=10
quarkus.datasource."dbr4".jdbc.max-lifetime=1800S

#
#Datasource SLAVES
#
quarkus.datasource."dbr1".db-kind=mysql
quarkus.datasource."dbr1".username=${DB_USERNAME}
quarkus.datasource."dbr1".password=${DB_PASSWORD}
quarkus.datasource."dbr1".jdbc.initial-size=5
quarkus.datasource."dbr1".jdbc.max-size=100
quarkus.datasource."dbr1".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr1".jdbc.url=jdbc:mysql://${DB_READ}:3306/mossbets_profile?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr1".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr1".jdbc.pooling-enabled=true
quarkus.datasource."dbr1".jdbc.min-size=1
quarkus.datasource."dbr1".jdbc.idle-removal-interval=50
quarkus.datasource."dbr1".jdbc.background-validation-interval=10
quarkus.datasource."dbr1".jdbc.leak-detection-interval=30
quarkus.datasource."dbr1".jdbc.acquisition-timeout=10
quarkus.datasource."dbr1".jdbc.max-lifetime=1800S

#Transactions
quarkus.datasource."dbr2".db-kind=mysql
quarkus.datasource."dbr2".username=${DB_USERNAME}
quarkus.datasource."dbr2".password=${DB_PASSWORD}
quarkus.datasource."dbr2".jdbc.initial-size=5
quarkus.datasource."dbr2".jdbc.max-size=100
quarkus.datasource."dbr2".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr2".jdbc.url=jdbc:mysql://${DB_READ}:3306/mossbets_transactions?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr2".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr2".jdbc.pooling-enabled=true
quarkus.datasource."dbr2".jdbc.min-size=1
quarkus.datasource."dbr2".jdbc.idle-removal-interval=50
quarkus.datasource."dbr2".jdbc.background-validation-interval=10
quarkus.datasource."dbr2".jdbc.leak-detection-interval=30
quarkus.datasource."dbr2".jdbc.acquisition-timeout=10
quarkus.datasource."dbr2".jdbc.max-lifetime=1800S

#Bets
quarkus.datasource."dbr3".db-kind=mysql
quarkus.datasource."dbr3".username=${DB_USERNAME}
quarkus.datasource."dbr3".password=${DB_PASSWORD}
quarkus.datasource."dbr3".jdbc.initial-size=5
quarkus.datasource."dbr3".jdbc.max-size=100
quarkus.datasource."dbr3".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr3".jdbc.url=jdbc:mysql://${DB_READ}:3306/mossbets_bets?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr3".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr3".jdbc.pooling-enabled=true
quarkus.datasource."dbr3".jdbc.min-size=1
quarkus.datasource."dbr3".jdbc.idle-removal-interval=50
quarkus.datasource."dbr3".jdbc.background-validation-interval=10
quarkus.datasource."dbr3".jdbc.leak-detection-interval=30
quarkus.datasource."dbr3".jdbc.acquisition-timeout=10
quarkus.datasource."dbr3".jdbc.max-lifetime=1800S

#Write Databases
%db1.quarkus.datasource."db1".active=true
%db2.quarkus.datasource."db2".active=true
%db3.quarkus.datasource."db3".active=true

#Read Databases
%dbr1.quarkus.datasource."dbr1".active=true
%dbr2.quarkus.datasource."dbr2".active=true
%dbr3.quarkus.datasource."dbr3".active=true

quarkus.datasource.devservices.enabled=false

#logging
quarkus.log.file.level=ALL
quarkus.log.file.enable=true
quarkus.log.console.enable=false
quarkus.log.file.path=/var/tmp/log/java/app.log
quarkus.log.file.format=%d{yyyy-MM-dd HH:mm:ss,SSS} : %-5p : %m%n
quarkus.log.file.rotation.max-file-size=5000M
quarkus.log.file.rotation.max-backup-index=10
quarkus.log.file.rotation.file-suffix=.yyyy-MM-dd
quarkus.log.file.rotation.rotate-on-boot=true

#Redis
mbs.redis-port=6379
mbs.redis-user=${REDIS_USER}
mbs.redis-auth=${REDIS_AUTH}
mbs.redis-host=${REDIS_HOST:ke-pr-redis-ha-1-node-0}

mbs.number-of-threads=200

#Rabbit
mbs.rabbit-mq-port=5672
mbs.rabbit-mq-password=${RABBIT_PASSWORD}
mbs.rabbit-mq-username=${RABBIT_USERNAME}
mbs.rabbit-mq-host=${RABBIT_HOST:rabbitmq-cluster-1-node-0}

#mail
mbs.server-name=${SERVERNAME}
mbs.launch-url=${LAUNCHURL}
mbs.cipher-url=${CIPHERURL}
mbs.encryption-key=${ENVRYPTIONKEY}

quarkus.application.name=mbs-quarkus-app
quarkus.otel.enabled=false